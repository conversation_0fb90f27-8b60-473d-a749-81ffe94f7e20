using System;
using System.Threading.Tasks;
using Alpaca.Markets;
using Microsoft.Extensions.Logging;

namespace SmaTrendFollower.Console.Commands;

/// <summary>
/// Direct paper trading test command for validating Alpaca API connectivity
/// </summary>
public static class DirectTradeTest
{
    /// <summary>
    /// Executes a direct paper trade test to validate Alpaca API functionality
    /// </summary>
    public static async Task ExecuteDirectPaperTradeAsync()
    {
        System.Console.WriteLine("🧪 Direct Paper Trading Test");
        System.Console.WriteLine(new string('=', 50));

        try
        {
            // Force paper trading environment
            Environment.SetEnvironmentVariable("APCA_API_ENV", "paper");
            
            // Paper trading credentials
            var keyId = "PK0AM3WB1CES3YBQPGR0";
            var secretKey = "2O4bJsHpGjyYU6FvQ956kIJaLePdBihZveKutbtf";
            
            System.Console.WriteLine($"📊 Using paper trading credentials: {keyId}");
            
            // Create Alpaca client for paper trading
            var alpacaEnvironment = Environments.Paper;
            var tradingClient = alpacaEnvironment.GetAlpacaTradingClient(new SecretKey(keyId, secretKey));
            
            // Test 1: Get account info
            System.Console.WriteLine("🔍 Testing account access...");
            var account = await tradingClient.GetAccountAsync();
            
            System.Console.WriteLine("✅ Paper account connected successfully!");
            System.Console.WriteLine($"   Account ID: {account.AccountId}");
            System.Console.WriteLine($"   Buying Power: ${account.BuyingPower:F2}");
            System.Console.WriteLine($"   Portfolio Value: ${account.Equity:F2}");
            System.Console.WriteLine($"   Account Status: {account.Status}");
            
            // Test 2: Get current positions
            System.Console.WriteLine("📊 Checking current positions...");
            var positions = await tradingClient.ListPositionsAsync();
            
            if (positions.Any())
            {
                System.Console.WriteLine($"   Found {positions.Count} positions:");
                foreach (var position in positions.Take(5))
                {
                    System.Console.WriteLine($"   - {position.Symbol}: {position.Quantity} shares @ ${position.AverageEntryPrice:F2}");
                }
            }
            else
            {
                System.Console.WriteLine("   No current positions found");
            }
            
            // Test 3: Submit a paper trade (only if account allows trading)
            if (account.Status == AccountStatus.Active)
            {
                System.Console.WriteLine("🚀 Submitting paper trade order...");
                var orderRequest = new NewOrderRequest("AAPL", 1, OrderSide.Buy, OrderType.Market, TimeInForce.Day);
                var order = await tradingClient.PostOrderAsync(orderRequest);
                
                System.Console.WriteLine("✅ Paper trade order submitted successfully!");
                System.Console.WriteLine($"   Order ID: {order.OrderId}");
                System.Console.WriteLine($"   Symbol: {order.Symbol}");
                System.Console.WriteLine($"   Quantity: {order.Quantity} shares");
                System.Console.WriteLine($"   Side: {order.OrderSide}");
                System.Console.WriteLine($"   Type: {order.OrderType}");
                System.Console.WriteLine($"   Status: {order.OrderStatus}");
                System.Console.WriteLine($"   Submitted At: {order.SubmittedAtUtc}");
                
                // Wait and check order status
                System.Console.WriteLine("⏳ Waiting 3 seconds to check order status...");
                await Task.Delay(3000);
                
                var updatedOrder = await tradingClient.GetOrderAsync(order.OrderId);
                System.Console.WriteLine($"📊 Updated Order Status: {updatedOrder.OrderStatus}");
                
                if (updatedOrder.FilledQuantity > 0)
                {
                    System.Console.WriteLine($"✅ Order filled: {updatedOrder.FilledQuantity} shares @ ${updatedOrder.AverageFillPrice:F2}");
                    System.Console.WriteLine($"💰 Total Value: ${updatedOrder.FilledQuantity * updatedOrder.AverageFillPrice:F2}");
                }
                else
                {
                    System.Console.WriteLine("⏳ Order still pending - this is normal for paper trading");
                }
            }
            else
            {
                System.Console.WriteLine("⚠️ Account trading is blocked - skipping order submission test");
                System.Console.WriteLine("   This may be due to account restrictions or liquidation-only mode");
            }
            
            System.Console.WriteLine();
            System.Console.WriteLine("🎉 PAPER TRADING TEST COMPLETED SUCCESSFULLY!");
            System.Console.WriteLine("🎯 The Alpaca paper trading API is working correctly!");
            System.Console.WriteLine("🚀 SmaTrendFollower can now execute paper trades!");
            
        }
        catch (Exception ex)
        {
            System.Console.WriteLine($"❌ Error in paper trading test: {ex.Message}");
            System.Console.WriteLine($"Stack trace: {ex.StackTrace}");
            throw;
        }
    }
}
