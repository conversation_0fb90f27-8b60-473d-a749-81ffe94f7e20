{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft": "Warning",
      "Microsoft.Hosting.Lifetime": "Information"
    }
  },
  "Redis": {
    "ConnectionString": "localhost:6379",
    "UniverseCacheConnection": "localhost:6379"
  },
  "UniverseCache": {
    "RefreshInterval": "00:10:00"
  },
  "AlpacaNews": {
    "Endpoint": "wss://stream.data.alpaca.markets/v1beta1/news",
    "KeyIdEnv": "APCA_API_KEY_ID",
    "SecretEnv": "APCA_API_SECRET"
  },
  "Gemini": {
    "Endpoint": "https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent",
    "ApiKeyEnv": "GEMINI_API_KEY",
    "TimeoutSec": 45
  },
  "QuoteVolatility": {
    "WindowSeconds": 120,
    "StdDevSigma": 2.0,
    "HaltDurationSeconds": 120
  },
  "OptionsWheel": {
    "StrikeBandPercent": 15
  },
  "Slippage": {
    "ModelPath": "Model/slippage_model.zip",
    "DefaultCents": 0.3
  },

  // Enhanced Services Feature Flags
  "EnhancedServices": {
    "EnableEnhancedDataRetrieval": true,
    "EnableAdaptiveRateLimit": true,
    "EnableAdaptiveSignalGeneration": true
  },

  // Enhanced Data Retrieval Configuration
  "EnhancedDataRetrieval": {
    "MaxConcurrentRequests": 20,
    "PrimaryApiTimeout": "00:00:45",
    "BatchTimeout": "00:03:00",
    "RelaxedStalenessThreshold": "02:00:00",
    "EmergencyModeMaxStaleness": "1.00:00:00",
    "EmergencyModeTimeout": "00:15:00",
    "EnableSyntheticData": true,
    "MinimumBatchSuccessRate": 0.7,
    "MaxFailedAttempts": 3
  },

  // Synthetic Data Generation Configuration
  "SyntheticData": {
    "RandomSeed": null,
    "DefaultStartPrice": 100.0,
    "VolumeMultiplier": 0.5,
    "MaxCorrelation": 0.95,
    "MinCorrelation": 0.1,
    "DefaultVolatility": 0.02,
    "UseSectorCorrelations": true
  },

  // Flexible Staleness Configuration
  "FlexibleStaleness": {
    "DefaultStalenessThreshold": "00:18:00",
    "AfterHoursStalenessThreshold": "08:00:00",
    "EmergencyModeStalenessThreshold": "1.00:00:00",
    "EnableFlexibleStaleness": true
  },

  // Adaptive Rate Limiting Configuration
  "AdaptiveRateLimit": {
    "AdjustmentInterval": "00:02:00",
    "MinRequestsPerSecond": 1,
    "MaxRequestsPerSecond": 100,
    "SuccessRateThreshold": 0.95,
    "ErrorRateThreshold": 0.05,
    "BackoffMultiplier": 0.8,
    "RecoveryMultiplier": 1.1
  },

  // Adaptive Signal Generation Configuration
  "AdaptiveSignal": {
    "MaxSymbolsToProcess": 500,
    "MinConfidenceScore": 0.5,
    "EnableSyntheticData": true,
    "EnableFallbackStrategies": true
  },

  "AllowedHosts": "*"
}
